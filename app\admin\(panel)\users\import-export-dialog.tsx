"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Dropzone, DropzoneEmptyState } from "@/components/ui/dropzone";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import { exportUsers, generateTemplateFile } from "@/service/export";
import { analyzeImportData } from "@/service/import";
import { ImportMode } from "@/service/import-utils";
import { useRouter } from "next/navigation";
import {
  Download,
  FileSpreadsheet,
  FileUp,
  Info,
  ChevronDown,
  ChevronRight,
} from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";

interface ImportExportDialogProps {
  onImportComplete: () => Promise<void>;
}

export default function ImportExportDialog({
  onImportComplete,
}: ImportExportDialogProps) {
  const router = useRouter();
  const [isOpen, setIsOpen] = useState(false);
  const [isImportDialogOpen, setIsImportDialogOpen] = useState(false);
  const [isExporting, setIsExporting] = useState(false);
  const [isImporting, setIsImporting] = useState(false);
  const [importMode, setImportMode] = useState<ImportMode>(
    ImportMode.PARTIAL_UPDATE
  );
  const [isRulesExpanded, setIsRulesExpanded] = useState(false);
  const [isFieldsExpanded, setIsFieldsExpanded] = useState(false);

  // 处理导出
  const handleExport = async () => {
    try {
      setIsExporting(true);
      const result = await exportUsers();

      // 打开下载链接
      if (result.downloadUrl) {
        window.open(result.downloadUrl, "_blank");
      }

      toast.success("导出成功");
    } catch (error) {
      console.error("导出失败:", error);
      toast.error("导出失败");
    } finally {
      setIsExporting(false);
    }
  };

  // 处理下载模板
  const handleDownloadTemplate = async () => {
    try {
      const result = await generateTemplateFile();

      // 打开下载链接
      if (result.downloadUrl) {
        window.open(result.downloadUrl, "_blank");
      }

      toast.success("模板下载成功");
    } catch (error) {
      console.error("模板下载失败:", error);
      toast.error("模板下载失败");
    }
  };

  // 处理导入
  const handleImportClick = () => {
    setIsOpen(false);
    setTimeout(() => setIsImportDialogOpen(true), 100);
  };

  // 处理文件上传
  const handleFileDrop = async (acceptedFiles: File[]) => {
    if (acceptedFiles.length === 0) return;

    try {
      setIsImporting(true);
      const file = acceptedFiles[0];

      // 分析导入数据
      const analysisResult = await analyzeImportData(file, importMode);

      // 将分析结果存储到 sessionStorage
      sessionStorage.setItem(
        "importAnalysisResult",
        JSON.stringify(analysisResult)
      );

      // 关闭对话框并跳转到预览页面
      setIsImportDialogOpen(false);
      setIsOpen(false);
      router.push("/admin/users/preview");
    } catch (error) {
      console.error("分析导入数据失败:", error);
      toast.error(
        `分析失败: ${error instanceof Error ? error.message : "未知错误"}`
      );
    } finally {
      setIsImporting(false);
    }
  };

  return (
    <>
      <Dialog
        open={isOpen}
        onOpenChange={(open) => {
          setIsOpen(open);
        }}
      >
        <DialogTrigger asChild>
          <Button variant="outline" className="mr-2">
            <FileSpreadsheet className="mr-2 h-4 w-4" />
            导入/导出
          </Button>
        </DialogTrigger>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>用户数据导入导出</DialogTitle>
            <DialogDescription>
              导入或导出用户数据，支持Excel格式
            </DialogDescription>
          </DialogHeader>

          <Tabs defaultValue="import" className="mt-4">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="import">导入数据</TabsTrigger>
              <TabsTrigger value="export">导出数据</TabsTrigger>
            </TabsList>

            <TabsContent value="export" className="space-y-4 py-4">
              <div className="space-y-4">
                <div className="rounded-md border p-4">
                  <h3 className="mb-2 font-medium">导出内容</h3>
                  <p className="text-muted-foreground text-sm">
                    导出的Excel文件包含所有用户的基本信息和名片信息。
                  </p>
                </div>

                <Button
                  onClick={handleExport}
                  className="w-full"
                  disabled={isExporting}
                >
                  <Download className="mr-2 h-4 w-4" />
                  {isExporting ? "导出中..." : "导出用户数据"}
                </Button>
              </div>
            </TabsContent>

            <TabsContent value="import" className="space-y-4 py-4">
              <div className="space-y-4">
                <div className="rounded-md border p-4">
                  <h3 className="mb-2 font-medium">📥 导入用户数据</h3>
                  <p className="text-muted-foreground text-sm">
                    基于工号精确匹配的用户数据导入。请下载模板文件，按照格式填写后上传。
                  </p>
                  <div className="mt-3 rounded-md bg-green-50 p-3">
                    <h4 className="mb-2 text-sm font-medium text-green-900">
                      🎯 匹配规则
                    </h4>
                    <p className="text-green-800 text-xs">
                      • 系统通过 <strong>工号</strong> 精确匹配现有用户
                    </p>
                    <p className="text-green-800 text-xs">
                      • 如果工号已存在：根据选择的模式更新用户信息
                    </p>
                    <p className="text-green-800 text-xs">
                      • 如果工号不存在：创建新用户（总是使用完整信息）
                    </p>
                    <p className="text-green-800 text-xs">
                      • <strong>无冲突：</strong>
                      基于工号唯一性，不会出现重复或冲突
                    </p>
                  </div>
                </div>

                <div className="flex space-x-2">
                  <Button
                    onClick={handleImportClick}
                    className="flex-1"
                    variant="default"
                  >
                    <FileUp className="mr-2 h-4 w-4" />
                    从表格导入
                  </Button>
                  <Button
                    onClick={handleDownloadTemplate}
                    className="flex-1"
                    variant="outline"
                  >
                    <FileSpreadsheet className="mr-2 h-4 w-4" />
                    获取表格模板
                  </Button>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </DialogContent>
      </Dialog>

      <Dialog open={isImportDialogOpen} onOpenChange={setIsImportDialogOpen}>
        <DialogContent className="sm:max-w-[700px] max-h-[90vh] flex flex-col">
          <DialogHeader>
            <DialogTitle>导入用户数据</DialogTitle>
            <DialogDescription>
              请选择或拖放Excel文件到下方区域
            </DialogDescription>
          </DialogHeader>

          <div className="flex-1 overflow-y-auto space-y-4 pr-2">
            <div className="rounded-md border p-4">
              <h3 className="mb-3 font-medium">导入模式</h3>
              <RadioGroup
                value={importMode}
                onValueChange={(value) => setImportMode(value as ImportMode)}
                className="space-y-3"
              >
                <div className="flex items-start space-x-3">
                  <RadioGroupItem
                    value={ImportMode.PARTIAL_UPDATE}
                    id="partial"
                    className="mt-0.5"
                  />
                  <div className="space-y-1">
                    <Label
                      htmlFor="partial"
                      className="cursor-pointer font-medium"
                    >
                      部分更新（推荐）
                    </Label>
                    <p className="text-muted-foreground text-sm">
                      只更新Excel中有内容的字段，空白字段保持原有数据不变
                    </p>
                    <div className="mt-2 rounded-md bg-green-50 p-2">
                      <p className="text-green-700 text-xs">
                        <strong>示例：</strong>
                        如果只填写了"公司"字段，则只更新公司信息，其他字段（姓名、部门等）保持不变
                      </p>
                    </div>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <RadioGroupItem
                    value={ImportMode.FULL_OVERWRITE}
                    id="full"
                    className="mt-0.5"
                  />
                  <div className="space-y-1">
                    <Label
                      htmlFor="full"
                      className="cursor-pointer font-medium"
                    >
                      完全覆盖
                    </Label>
                    <p className="text-muted-foreground text-sm">
                      更新所有字段，空白字段会清空原有数据
                    </p>
                    <div className="mt-2 rounded-md bg-orange-50 p-2">
                      <p className="text-orange-700 text-xs">
                        <strong>注意：</strong>
                        如果某个字段在Excel中为空，该字段在数据库中的原有数据会被清空
                      </p>
                    </div>
                  </div>
                </div>
              </RadioGroup>
            </div>

            <div className="py-4">
              <Dropzone
                accept={{
                  "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet":
                    [".xlsx"],
                  "application/vnd.ms-excel": [".xls"],
                }}
                onDrop={handleFileDrop}
                disabled={isImporting}
                className="h-40"
              >
                <DropzoneEmptyState>
                  <div className="flex flex-col items-center justify-center">
                    <FileSpreadsheet className="mb-2 h-8 w-8 text-muted-foreground" />
                    <p className="mb-1 font-medium">
                      {isImporting ? "导入中..." : "选择Excel文件"}
                    </p>
                    <p className="text-muted-foreground text-xs">
                      拖放文件到此处或点击选择
                    </p>
                    <p className="mt-2 text-muted-foreground text-xs">
                      支持 .xlsx 和 .xls 格式
                    </p>
                  </div>
                </DropzoneEmptyState>
              </Dropzone>
            </div>

            {/* 快速说明 */}
            <div className="rounded-md border border-green-200 bg-green-50 p-3">
              <div className="flex items-start space-x-2">
                <Info className="h-4 w-4 mt-0.5 flex-shrink-0 text-green-600" />
                <div className="text-sm">
                  <p className="font-medium text-green-900 mb-1">🎯 核心规则</p>
                  <p className="text-green-800 text-xs">
                    <strong>工号</strong> 为必填字段。<strong>姓名</strong>{" "}
                    在创建新用户或完全覆盖模式下必填，部分更新模式下可选。系统根据工号精确匹配：存在则更新，不存在则创建新用户。
                  </p>
                  <p className="text-green-700 text-xs mt-1">
                    💡 点击下方"详细规则说明"和"字段说明表"查看完整信息
                  </p>
                </div>
              </div>
            </div>

            <Collapsible
              open={isRulesExpanded}
              onOpenChange={setIsRulesExpanded}
            >
              <div className="rounded-md border border-blue-200 bg-blue-50 p-4">
                <CollapsibleTrigger className="flex items-center justify-between w-full text-left">
                  <div className="flex items-center space-x-2">
                    <span className="font-medium text-blue-900">
                      📋 详细规则说明
                    </span>
                  </div>
                  {isRulesExpanded ? (
                    <ChevronDown className="h-4 w-4 text-blue-600" />
                  ) : (
                    <ChevronRight className="h-4 w-4 text-blue-600" />
                  )}
                </CollapsibleTrigger>

                <CollapsibleContent className="mt-3">
                  <div className="space-y-3 text-sm">
                    <div className="space-y-2">
                      <p className="font-medium text-blue-900">📋 必填字段</p>
                      <ul className="space-y-1 text-blue-800 ml-4">
                        <li>
                          • <strong>工号：</strong>
                          作为用户唯一标识，必须填写且不能为空
                        </li>
                        <li>
                          • <strong>姓名：</strong>
                          创建新用户或完全覆盖模式下必填，部分更新模式下可选
                        </li>
                      </ul>
                    </div>

                    <div className="space-y-2">
                      <p className="font-medium text-blue-900">🔄 匹配逻辑</p>
                      <ul className="space-y-1 text-blue-800 ml-4">
                        <li>
                          • <strong>更新用户：</strong>
                          系统中已存在相同工号的用户，将更新其信息
                        </li>
                        <li>
                          • <strong>创建用户：</strong>
                          系统中不存在该工号，将创建新用户
                        </li>
                        <li>
                          • <strong>无冲突：</strong>
                          基于工号精确匹配，不会出现重复或冲突
                        </li>
                      </ul>
                    </div>

                    <div className="space-y-2">
                      <p className="font-medium text-blue-900">📱 手机号处理</p>
                      <ul className="space-y-1 text-blue-800 ml-4">
                        <li>
                          • <strong>登录手机号：</strong>
                          用于系统登录，如未提供则使用名片手机号
                        </li>
                        <li>
                          • <strong>名片手机号：</strong>显示在名片上的联系方式
                        </li>
                        <li>
                          • <strong>格式要求：</strong>
                          11位数字，以1开头（如：13812345678）
                        </li>
                      </ul>
                    </div>

                    <div className="space-y-2">
                      <p className="font-medium text-blue-900">⚙️ 更新模式</p>
                      <ul className="space-y-1 text-blue-800 ml-4">
                        <li>
                          • <strong>部分更新：</strong>
                          只更新Excel中有内容的字段，空白字段保持原值
                        </li>
                        <li>
                          • <strong>完全覆盖：</strong>
                          更新所有字段，空白字段会清空原有数据
                        </li>
                      </ul>
                    </div>
                  </div>
                </CollapsibleContent>
              </div>
            </Collapsible>

            <Collapsible
              open={isFieldsExpanded}
              onOpenChange={setIsFieldsExpanded}
            >
              <div className="rounded-md border border-gray-200 bg-gray-50 p-4">
                <CollapsibleTrigger className="flex items-center justify-between w-full text-left">
                  <span className="font-medium text-gray-900">
                    📊 字段说明表
                  </span>
                  {isFieldsExpanded ? (
                    <ChevronDown className="h-4 w-4 text-gray-600" />
                  ) : (
                    <ChevronRight className="h-4 w-4 text-gray-600" />
                  )}
                </CollapsibleTrigger>

                <CollapsibleContent className="mt-3">
                  <div className="overflow-x-auto">
                    <table className="w-full text-xs border-collapse">
                      <thead>
                        <tr className="border-b border-gray-300">
                          <th className="text-left p-2 font-medium text-gray-900">
                            字段名称
                          </th>
                          <th className="text-left p-2 font-medium text-gray-900">
                            是否必填
                          </th>
                          <th className="text-left p-2 font-medium text-gray-900">
                            说明
                          </th>
                          <th className="text-left p-2 font-medium text-gray-900">
                            示例
                          </th>
                        </tr>
                      </thead>
                      <tbody className="text-gray-700">
                        <tr className="border-b border-gray-200 bg-red-50">
                          <td className="p-2 font-medium">工号</td>
                          <td className="p-2 text-red-600 font-medium">必填</td>
                          <td className="p-2">
                            用户唯一标识，用于匹配现有用户
                          </td>
                          <td className="p-2 font-mono">EMP001</td>
                        </tr>
                        <tr className="border-b border-gray-200 bg-yellow-50">
                          <td className="p-2 font-medium">姓名</td>
                          <td className="p-2 text-orange-600 font-medium">
                            条件必填
                          </td>
                          <td className="p-2">
                            用户真实姓名，创建新用户或完全覆盖模式下必填
                          </td>
                          <td className="p-2">张三</td>
                        </tr>
                        <tr className="border-b border-gray-200">
                          <td className="p-2">登录手机号</td>
                          <td className="p-2 text-gray-500">可选</td>
                          <td className="p-2">
                            系统登录凭证，未填写则使用名片手机号
                          </td>
                          <td className="p-2 font-mono">13800138000</td>
                        </tr>
                        <tr className="border-b border-gray-200">
                          <td className="p-2">手机号</td>
                          <td className="p-2 text-gray-500">可选</td>
                          <td className="p-2">名片上显示的手机号</td>
                          <td className="p-2 font-mono">13800138000</td>
                        </tr>
                        <tr className="border-b border-gray-200">
                          <td className="p-2">公司</td>
                          <td className="p-2 text-gray-500">可选</td>
                          <td className="p-2">所属公司名称</td>
                          <td className="p-2">示例科技有限公司</td>
                        </tr>
                        <tr className="border-b border-gray-200">
                          <td className="p-2">部门</td>
                          <td className="p-2 text-gray-500">可选</td>
                          <td className="p-2">所属部门</td>
                          <td className="p-2">技术研发部</td>
                        </tr>
                        <tr className="border-b border-gray-200">
                          <td className="p-2">职务</td>
                          <td className="p-2 text-gray-500">可选</td>
                          <td className="p-2">具体职务名称</td>
                          <td className="p-2">高级软件工程师</td>
                        </tr>
                        <tr className="border-b border-gray-200">
                          <td className="p-2">职位</td>
                          <td className="p-2 text-gray-500">可选</td>
                          <td className="p-2">职位级别</td>
                          <td className="p-2">技术专家</td>
                        </tr>
                        <tr className="border-b border-gray-200">
                          <td className="p-2">座机</td>
                          <td className="p-2 text-gray-500">可选</td>
                          <td className="p-2">办公室座机号码</td>
                          <td className="p-2 font-mono">010-12345678</td>
                        </tr>
                        <tr className="border-b border-gray-200">
                          <td className="p-2">邮箱</td>
                          <td className="p-2 text-gray-500">可选</td>
                          <td className="p-2">电子邮箱地址</td>
                          <td className="p-2 font-mono">
                            <EMAIL>
                          </td>
                        </tr>
                        <tr className="border-b border-gray-200">
                          <td className="p-2">地址</td>
                          <td className="p-2 text-gray-500">可选</td>
                          <td className="p-2">办公地址或联系地址</td>
                          <td className="p-2">北京市朝阳区科技园区123号</td>
                        </tr>
                        <tr>
                          <td className="p-2">头像URL</td>
                          <td className="p-2 text-gray-500">可选</td>
                          <td className="p-2">头像图片的网络地址</td>
                          <td className="p-2 font-mono text-xs">
                            https://example.com/avatar.jpg
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </CollapsibleContent>
              </div>
            </Collapsible>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
}

"use client"

import { DashboardSkeleton } from "@/components/skeletons/dashboard-skeleton"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { getDashboardStats } from "@/service/dashboard"
import { Suspense, useEffect, useState } from "react"

interface DashboardStats {
  totalUsers: number
  totalCards: number
  totalConfigs: number
}

// 仪表盘内容组件
function DashboardContent() {
  const [stats, setStats] = useState<DashboardStats>({
    totalUsers: 0,
    totalCards: 0,
    totalConfigs: 0,
  })
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    async function fetchStats() {
      try {
        // 从服务器获取真实统计数据
        const dashboardStats = await getDashboardStats()
        setStats(dashboardStats)
      } catch (error) {
        console.error("获取统计数据失败:", error)
      } finally {
        setLoading(false)
      }
    }

    fetchStats()
  }, [])

  if (loading) {
    return <DashboardSkeleton />
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="font-bold text-3xl">仪表盘</h1>
        <p className="mt-2 text-gray-500">管理系统统计概览</p>
      </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="font-medium text-gray-500 text-sm">
              总用户数
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="font-bold text-3xl">{stats.totalUsers}</div>
            <p className="mt-1 text-gray-500 text-sm">系统中的用户总数</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="font-medium text-gray-500 text-sm">
              总名片数
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="font-bold text-3xl">{stats.totalCards}</div>
            <p className="mt-1 text-gray-500 text-sm">系统中的名片总数</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="font-medium text-gray-500 text-sm">
              名片配置总数
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="font-bold text-3xl">{stats.totalConfigs}</div>
            <p className="mt-1 text-gray-500 text-sm">用户创建的名片配置总数</p>
          </CardContent>
        </Card>
      </div>

      <div className="mt-8">
        <Card>
          <CardHeader>
            <CardTitle>系统说明</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <p>
                欢迎使用JCEC电子名片管理系统。这是一个管理面板，您可以在这里管理用户、名片信息和名片配置。
              </p>
              <p>使用左侧的导航菜单可以进入不同的管理模块：</p>
              <ul className="list-disc space-y-2 pl-5">
                <li>
                  <strong>用户管理</strong>
                  ：查看和管理系统用户，包括创建新用户、编辑用户信息、查看用户名片和删除用户。
                </li>
                <li>
                  <strong>名片管理</strong>
                  ：查看用户的名片信息和配置，可以设置名片配置的公开状态。
                </li>
              </ul>
              <p className="mt-4 text-gray-500">系统说明：</p>
              <ul className="list-disc space-y-2 pl-5 text-gray-500">
                <li>用户可以通过微信小程序登录系统，创建和管理自己的名片。</li>
                <li>
                  用户可以在小程序中创建多种名片配置，选择显示或隐藏特定的名片信息。
                </li>
                <li>
                  管理员可以在此后台系统中管理用户和名片，并控制名片配置的公开状态。
                </li>
                <li>
                  名片的编辑和配置创建功能只能在小程序端进行，后台仅提供查看和管理功能。
                </li>
              </ul>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

// 主页面组件
export default function Dashboard() {
  return (
    <Suspense fallback={<DashboardSkeleton />}>
      <DashboardContent />
    </Suspense>
  )
}

"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { getProcessedImageUrl } from "@/lib/imageUtils"
import { Eye } from "lucide-react"
import { useState } from "react"
import type { UserForm } from "./user-table"

interface ViewCardDialogProps {
  user: UserForm
}

export default function ViewCardDialog({ user }: ViewCardDialogProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [isPreviewOpen, setIsPreviewOpen] = useState(false)

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm" className="flex items-center">
          <Eye className="mr-1 h-4 w-4" />
          查看名片
        </Button>
      </DialogTrigger>
      <DialogContent className="flex max-h-[90vh] flex-col overflow-hidden sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>{user.cardInfo?.name || "用户"} 的名片信息</DialogTitle>
          <DialogDescription>查看用户的详细名片信息</DialogDescription>
        </DialogHeader>
        <div className="flex-1 overflow-y-auto pr-1">
          {!user.cardInfo ? (
            <div className="flex items-center justify-center py-8">
              <p className="text-gray-500">该用户暂无名片信息</p>
            </div>
          ) : (
            <div className="space-y-4 py-4">
              {user.cardInfo.avatar && (
                <div className="mb-4 flex justify-center">
                  <button
                    type="button"
                    className="relative h-32 cursor-pointer overflow-hidden rounded-lg"
                    onClick={() => setIsPreviewOpen(true)}
                  >
                    <img
                      src={getProcessedImageUrl(user.cardInfo.avatar, {
                        width: 200,
                        height: 200,
                        format: "webp",
                      })}
                      alt="头像"
                      className="h-full object-cover"
                    />
                  </button>
                </div>
              )}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h4 className="font-medium text-gray-500 text-sm">姓名</h4>
                  <p>{user.cardInfo.name || "-"}</p>
                </div>
                <div>
                  <h4 className="font-medium text-gray-500 text-sm">公司</h4>
                  <p>{user.cardInfo.company || "-"}</p>
                </div>
                <div>
                  <h4 className="font-medium text-gray-500 text-sm">职务</h4>
                  <p>{user.cardInfo.position || "-"}</p>
                </div>
                <div>
                  <h4 className="font-medium text-gray-500 text-sm">职位</h4>
                  <p>{user.cardInfo.title || "-"}</p>
                </div>
                <div>
                  <h4 className="font-medium text-gray-500 text-sm">部门</h4>
                  <p>{user.cardInfo.department || "-"}</p>
                </div>
                <div>
                  <h4 className="font-medium text-gray-500 text-sm">手机号</h4>
                  <p>{user.cardInfo.mobile || "-"}</p>
                </div>
                <div>
                  <h4 className="font-medium text-gray-500 text-sm">座机</h4>
                  <p>{user.cardInfo.telephone || "-"}</p>
                </div>
                <div>
                  <h4 className="font-medium text-gray-500 text-sm">邮箱</h4>
                  <p>{user.cardInfo.email || "-"}</p>
                </div>
                <div className="col-span-2">
                  <h4 className="font-medium text-gray-500 text-sm">地址</h4>
                  <p>{user.cardInfo.address || "-"}</p>
                </div>
                {user.cardInfo.avatar && (
                  <div className="col-span-2">
                    <h4 className="font-medium text-gray-500 text-sm">头像URL</h4>
                    <p className="break-all text-xs">{user.cardInfo.avatar}</p>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </DialogContent>

      {/* 头像预览对话框 */}
      <Dialog open={isPreviewOpen} onOpenChange={setIsPreviewOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>头像预览</DialogTitle>
          </DialogHeader>
          <div className="flex justify-center p-6">
            {user.cardInfo?.avatar && (
              <div className="relative max-h-[70vh] max-w-full overflow-hidden rounded-md">
                <img
                  src={getProcessedImageUrl(user.cardInfo.avatar, {
                    width: 800,
                    format: "webp",
                  })}
                  alt="头像预览"
                  className="max-h-[70vh] max-w-full object-contain"
                />
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>
    </Dialog>
  )
}

"use client"

import { CardDetailSkeleton } from "@/components/skeletons/card-detail-skeleton"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { Switch } from "@/components/ui/switch"
import type { CardConfig, CardInfo } from "@/database"
import { findCardConfigByCardId, updateCardConfig } from "@/service/cardConfig"
import { findCardInfoById } from "@/service/cardInfo"
import { ChevronLeft, ChevronRight } from "lucide-react"
import Link from "next/link"
import React, { useEffect, useState } from "react"
import { toast } from "sonner"

// 名片详情内容组件
export default function CardDetailPage({
  params,
}: {
  params: Promise<{ id: string }>
}) {
  const [cardInfo, setCardInfo] = useState<CardInfo | null>(null)
  const [cardConfigs, setCardConfigs] = useState<CardConfig[]>([])
  const [loading, setLoading] = useState(true)
  const [expandedConfigs, setExpandedConfigs] = useState<
    Record<string, boolean>
  >({})

  const { id } = React.use(params)

  // 获取名片信息和配置
  useEffect(() => {
    const fetchCardData = async () => {
      setLoading(true)
      try {
        // 获取名片信息
        const cardData = await findCardInfoById(id)
        setCardInfo(cardData as unknown as CardInfo)

        // 获取名片配置
        const configsData = await findCardConfigByCardId(id)
        setCardConfigs(configsData as unknown as CardConfig[])
      } catch (error) {
        console.error("获取名片数据失败:", error)
        toast.error("获取名片数据失败")
      } finally {
        setLoading(false)
      }
    }
    fetchCardData()
  }, [id])

  // 切换配置的展开/折叠状态
  const toggleConfigExpand = (configId: string) => {
    setExpandedConfigs((prev) => ({
      ...prev,
      [configId]: !prev[configId],
    }))
  }

  // 切换配置的公开状态
  const toggleConfigPublic = async (config: CardConfig) => {
    try {
      // 更新配置
      await updateCardConfig(config.id, {
        public: !config.public,
      })

      // 更新本地状态
      setCardConfigs((prev) =>
        prev.map((c) => (c.id === config.id ? { ...c, public: !c.public } : c))
      )

      toast.success("配置更新成功")
    } catch (error) {
      console.error("更新配置失败:", error)
      toast.error("更新配置失败")
    }
  }

  if (loading) {
    return <CardDetailSkeleton />
  }

  if (!cardInfo) {
    return (
      <div className="flex h-full items-center justify-center">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle className="text-center">未找到名片</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-center">未找到指定的名片信息。</p>
            <div className="mt-4 flex justify-center">
              <Button asChild>
                <Link href="/admin/cards">返回名片列表</Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="font-bold text-3xl">名片详情</h1>
          <p className="mt-1 text-gray-500">查看名片信息和配置</p>
        </div>
        <Button variant="outline" asChild>
          <Link href="/admin/cards" className="flex items-center">
            <ChevronLeft className="mr-1 h-4 w-4" />
            返回名片列表
          </Link>
        </Button>
      </div>

      {/* 名片基本信息 */}
      <Card>
        <CardHeader>
          <CardTitle>基本信息</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <h3 className="font-medium text-gray-500 text-sm">姓名</h3>
              <p>{cardInfo.name}</p>
            </div>
            <div>
              <h3 className="font-medium text-gray-500 text-sm">公司</h3>
              <p>{cardInfo.company || "-"}</p>
            </div>
            <div>
              <h3 className="font-medium text-gray-500 text-sm">职务</h3>
              <p>{cardInfo.position || "-"}</p>
            </div>
            <div>
              <h3 className="font-medium text-gray-500 text-sm">职位</h3>
              <p>{cardInfo.title || "-"}</p>
            </div>
            <div>
              <h3 className="font-medium text-gray-500 text-sm">部门</h3>
              <p>{cardInfo.department || "-"}</p>
            </div>
            <div>
              <h3 className="font-medium text-gray-500 text-sm">手机号</h3>
              <p>{cardInfo.mobile || "-"}</p>
            </div>
            <div>
              <h3 className="font-medium text-gray-500 text-sm">座机</h3>
              <p>{cardInfo.telephone || "-"}</p>
            </div>
            <div>
              <h3 className="font-medium text-gray-500 text-sm">邮箱</h3>
              <p>{cardInfo.email || "-"}</p>
            </div>
            <div>
              <h3 className="font-medium text-gray-500 text-sm">地址</h3>
              <p>{cardInfo.address || "-"}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 名片配置列表 */}
      <Card>
        <CardHeader>
          <CardTitle>名片配置</CardTitle>
        </CardHeader>
        <CardContent>
          {cardConfigs.length === 0 ? (
            <p className="text-center text-gray-500">该名片暂无配置</p>
          ) : (
            <div className="space-y-4">
              {cardConfigs.map((config) => (
                <div key={config.id} className="rounded-lg border p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <h3 className="font-medium">{config.name}</h3>
                      <span className="ml-2 text-gray-500 text-sm">
                        ({config.styleName})
                      </span>
                    </div>
                    <div className="flex items-center space-x-4">
                      <div className="flex items-center space-x-2">
                        <span className="text-sm">公开</span>
                        <Switch
                          checked={config.public}
                          onCheckedChange={() => toggleConfigPublic(config)}
                        />
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => toggleConfigExpand(config.id)}
                      >
                        <ChevronRight
                          className={`h-4 w-4 transition-transform ${
                            expandedConfigs[config.id] ? "rotate-90" : ""
                          }`}
                        />
                      </Button>
                    </div>
                  </div>

                  {expandedConfigs[config.id] && (
                    <div className="mt-4 space-y-4">
                      <Separator />
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <h4 className="font-medium text-gray-500 text-sm">
                            显示头像
                          </h4>
                          <p>{config.showAvatar ? "是" : "否"}</p>
                        </div>
                        <div>
                          <h4 className="font-medium text-gray-500 text-sm">
                            显示姓名
                          </h4>
                          <p>{config.showName ? "是" : "否"}</p>
                        </div>
                        <div>
                          <h4 className="font-medium text-gray-500 text-sm">
                            显示部门
                          </h4>
                          <p>{config.showDepartment ? "是" : "否"}</p>
                        </div>
                        <div>
                          <h4 className="font-medium text-gray-500 text-sm">
                            显示职位
                          </h4>
                          <p>{config.showTitle ? "是" : "否"}</p>
                        </div>
                        <div>
                          <h4 className="font-medium text-gray-500 text-sm">
                            显示公司
                          </h4>
                          <p>{config.showCompany ? "是" : "否"}</p>
                        </div>
                        <div>
                          <h4 className="font-medium text-gray-500 text-sm">
                            显示手机号
                          </h4>
                          <p>{config.showMobile ? "是" : "否"}</p>
                        </div>
                        <div>
                          <h4 className="font-medium text-gray-500 text-sm">
                            显示座机
                          </h4>
                          <p>{config.showTelephone ? "是" : "否"}</p>
                        </div>
                        <div>
                          <h4 className="font-medium text-gray-500 text-sm">
                            显示邮箱
                          </h4>
                          <p>{config.showEmail ? "是" : "否"}</p>
                        </div>
                        <div>
                          <h4 className="font-medium text-gray-500 text-sm">
                            显示地址
                          </h4>
                          <p>{config.showAddress ? "是" : "否"}</p>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

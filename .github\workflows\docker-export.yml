name: Build and Export Docker Image

env:
  TARBALL_NAME: "jcec-ecard-manager.tar"

on:
  push:
    branches: [main]

jobs:
  build-export:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4

      - name: Build image
        run: docker build -t jcec-ecard-manager:latest .

      - name: Save image to tarball
        run: docker save jcec-ecard-manager:latest -o ${{ env.TARBALL_NAME }}

      - name: Copy to server via scp
        uses: appleboy/scp-action@v1
        with:
          host: ${{ secrets.HOST }}
          port: ${{ secrets.PORT }}
          username: ${{ secrets.USERNAME }}
          password: ${{ secrets.PASSWORD }}
          source: ${{ env.TARBALL_NAME }}
          target: ~/jcec/docker_image/
          overwrite: true

      - name: Load Docker image on server
        uses: appleboy/ssh-action@v1
        with:
          host: ${{ secrets.HOST }}
          port: ${{ secrets.PORT }}
          username: ${{ secrets.USERNAME }}
          password: ${{ secrets.PASSWORD }}
          script: |
            cd ~/jcec/docker_image/
            sudo docker rmi -f jcec-ecard-manager:latest || true
            sudo docker load -i ${{ env.TARBALL_NAME }}
            sudo docker tag jcec-ecard-manager:latest jcec-ecard-manager:latest

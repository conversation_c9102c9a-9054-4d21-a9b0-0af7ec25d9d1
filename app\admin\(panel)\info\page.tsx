"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  BatteryMedium,
  Code,
  Eye,
  FileText,
  Hash,
  Save,
  Signal,
  Trash2,
  X,
} from "lucide-react";
import { useCallback, useEffect, useState } from "react";
import { toast } from "sonner";
import { Editor } from "@monaco-editor/react";
import { saveInfo, getInfoList, getInfo, deleteInfo } from "./actions";

interface InfoItem {
  id: string;
  name: string;
  content: string;
  meta: unknown;
  createdAt: Date;
  updatedAt: Date;
}

export default function InfoEditor() {
  const [infoName, setInfoName] = useState("");
  const [content, setContent] = useState("");
  const [contentType, setContentType] = useState<"text" | "html" | "markdown">(
    "html"
  );
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [existingInfos, setExistingInfos] = useState<InfoItem[]>([]);
  const [isDeleting, setIsDeleting] = useState(false);
  const [deleteConfirmName, setDeleteConfirmName] = useState("");
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);

  // 编辑器配置
  const editorOptions = {
    selectOnLineNumbers: true,
    fontSize: 14,
    wordWrap: "on" as const,
    minimap: { enabled: false },
    scrollBeyondLastLine: false,
    automaticLayout: true,
    tabSize: 2,
    // 禁用中文全角字符的警告提示
    unicodeHighlight: {
      ambiguousCharacters: false,
      invisibleCharacters: true,
      nonBasicASCII: false,
    },
  };

  // 获取现有的info列表
  const fetchInfos = useCallback(async () => {
    try {
      const result = await getInfoList();
      if (result.data) {
        setExistingInfos(result.data);
      } else if (result.error) {
        toast.error(result.error);
      }
    } catch (error) {
      console.error("获取info列表失败:", error);
      toast.error("获取info列表失败");
    }
  }, []);

  // 加载指定的info
  const loadInfo = async (name: string) => {
    if (!name) return;

    setIsLoading(true);
    try {
      const result = await getInfo(name);
      if (result.data) {
        setContent(result.data.content);
        setInfoName(name);
        // 根据meta中的类型或内容判断类型
        const meta = result.data.meta as { type?: string } | null;
        if (meta?.type) {
          setContentType(meta.type as "text" | "html" | "markdown");
        } else {
          // 根据内容判断类型
          if (
            result.data.content.includes("<") &&
            result.data.content.includes(">")
          ) {
            setContentType("html");
          } else if (
            result.data.content.includes("#") ||
            result.data.content.includes("**")
          ) {
            setContentType("markdown");
          } else {
            setContentType("text");
          }
        }
      } else if (result.error) {
        toast.error(result.error);
      }
    } catch (error) {
      toast.error("加载失败");
      console.error("加载info失败:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // 保存info
  const handleSaveInfo = async () => {
    if (!infoName.trim()) {
      toast.error("请输入名称");
      return;
    }

    if (!content.trim()) {
      toast.error("请输入内容");
      return;
    }

    setIsSaving(true);
    try {
      const result = await saveInfo(infoName.trim(), content, contentType);

      if (result.success) {
        toast.success("保存成功");
        fetchInfos(); // 刷新列表
      } else if (result.error) {
        toast.error(result.error);
      }
    } catch (error) {
      toast.error("保存失败");
      console.error("保存info失败:", error);
    } finally {
      setIsSaving(false);
    }
  };

  // 删除info
  const handleDeleteInfo = async () => {
    if (!deleteConfirmName.trim()) {
      toast.error("请选择要删除的info");
      return;
    }

    setIsDeleting(true);
    try {
      const result = await deleteInfo(deleteConfirmName.trim());

      if (result.success) {
        toast.success("删除成功");
        // 如果删除的是当前编辑的info，清空编辑器
        if (infoName === deleteConfirmName) {
          setInfoName("");
          setContent("");
          setContentType("html");
        }
        setDeleteConfirmName("");
        setShowDeleteDialog(false);
        fetchInfos(); // 刷新列表
      } else if (result.error) {
        toast.error(result.error);
      }
    } catch (error) {
      toast.error("删除失败");
      console.error("删除info失败:", error);
    } finally {
      setIsDeleting(false);
    }
  };

  // 获取Monaco编辑器语言
  const getEditorLanguage = () => {
    switch (contentType) {
      case "html":
        return "html";
      case "markdown":
        return "markdown";
      default:
        return "plaintext";
    }
  };

  // 渲染预览内容
  const renderPreview = () => {
    switch (contentType) {
      case "html": {
        return (
          <div
            style={{
              fontSize: "0.8rem",
              lineHeight: "1.2rem",
            }}
            // biome-ignore lint/security/noDangerouslySetInnerHtml: <explanation>
            dangerouslySetInnerHTML={{ __html: content }}
          />
        );
      }
      case "markdown": {
        // 简单的markdown渲染（可以后续使用专门的markdown库）
        const htmlContent = content
          .replace(/^# (.*$)/gim, "<h1>$1</h1>")
          .replace(/^## (.*$)/gim, "<h2>$1</h2>")
          .replace(/^### (.*$)/gim, "<h3>$1</h3>")
          .replace(/\*\*(.*)\*\*/gim, "<strong>$1</strong>")
          .replace(/\*(.*)\*/gim, "<em>$1</em>");

        return (
          <div
            className="prose prose-sm max-w-none"
            // biome-ignore lint/security/noDangerouslySetInnerHtml: <explanation>
            dangerouslySetInnerHTML={{ __html: htmlContent }}
          />
        );
      }
      default:
        return <pre className="whitespace-pre-wrap text-sm">{content}</pre>;
    }
  };

  useEffect(() => {
    fetchInfos();
  }, [fetchInfos]);

  return (
    <div className="flex h-screen">
      {/* 左侧编辑器 */}
      <div className="flex-1 flex flex-col border-r">
        <div className="p-4 border-b bg-background">
          <div className="space-y-4">
            <div className="flex gap-4 items-end">
              <div className="flex-1">
                <Label htmlFor="name">名称</Label>
                <Input
                  id="name"
                  value={infoName}
                  onChange={(e) => setInfoName(e.target.value)}
                  placeholder="输入info名称"
                />
              </div>
              <div className="flex gap-2">
                <Button
                  onClick={handleSaveInfo}
                  disabled={isSaving}
                  className="gap-2"
                >
                  <Save className="w-4 h-4" />
                  {isSaving ? "保存中..." : "保存 / 更新"}
                </Button>
              </div>
            </div>

            <div>
              <Label>内容类型</Label>
              <RadioGroup
                value={contentType}
                onValueChange={(value) =>
                  setContentType(value as typeof contentType)
                }
                className="flex gap-4 mt-2"
              >
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="text" id="text" />
                  <Label htmlFor="text" className="flex items-center gap-1">
                    <FileText className="w-4 h-4" />
                    纯文本
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="html" id="html" />
                  <Label htmlFor="html" className="flex items-center gap-1">
                    <Code className="w-4 h-4" />
                    HTML
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="markdown" id="markdown" />
                  <Label htmlFor="markdown" className="flex items-center gap-1">
                    <Hash className="w-4 h-4" />
                    Markdown
                  </Label>
                </div>
              </RadioGroup>
            </div>

            {existingInfos.length > 0 && (
              <div>
                <Label>已有Info</Label>
                <div className="flex gap-2 mt-2 flex-wrap">
                  {existingInfos.map((info) => (
                    <div key={info.id} className="relative group">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => loadInfo(info.name)}
                        disabled={isLoading}
                        className="pr-8"
                      >
                        {info.name}
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          setDeleteConfirmName(info.name);
                          setShowDeleteDialog(true);
                        }}
                        disabled={isDeleting}
                        className="absolute right-0 top-0 h-full w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity hover:bg-red-100 hover:text-red-600"
                      >
                        <X className="w-3 h-3" />
                      </Button>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>

        <div className="flex-1">
          <Editor
            language={getEditorLanguage()}
            theme="light"
            value={content}
            options={editorOptions}
            onChange={(value: string | undefined) => setContent(value || "")}
          />
        </div>
      </div>

      {/* 右侧预览 */}
      <div className="w-96 flex flex-col">
        <div className="p-4 border-b bg-background">
          <div className="flex items-center gap-2">
            <Eye className="w-4 h-4" />
            <span className="font-medium">预览 (手机端)</span>
          </div>
        </div>

        {/* 手机框架 */}
        <div className="flex-1 p-4 bg-gray-100">
          <div className="mx-auto max-w-xs">
            {/* 手机外框 */}
            <div className="bg-black rounded-3xl p-2 shadow-xl">
              <div className="bg-white rounded-2xl h-[600px] overflow-hidden">
                {/* 手机状态栏 */}
                <div className="bg-black text-white text-xs px-4 py-1 flex justify-between">
                  <span>9:41</span>
                  <div className="flex items-center gap-1">
                    <Signal className="w-3 h-3" />
                    <BatteryMedium className="w-4 h-4" />
                  </div>
                </div>

                {/* 内容区域 */}
                <div className="p-4 h-full overflow-auto">
                  {content ? (
                    renderPreview()
                  ) : (
                    <div className="text-gray-400 text-center mt-8">
                      在左侧编辑器中输入内容以查看预览
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 删除确认对话框 */}
      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <DialogContent>
          <DialogHeader>
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center">
                <Trash2 className="w-5 h-5 text-red-600" />
              </div>
              <div className="flex-1">
                <DialogTitle>确认删除</DialogTitle>
                <DialogDescription>此操作无法撤销</DialogDescription>
              </div>
            </div>
          </DialogHeader>

          <div className="py-4">
            <p className="text-gray-700">
              确定要删除 "
              <span className="font-medium">{deleteConfirmName}</span>" 吗？
            </p>
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setShowDeleteDialog(false);
                setDeleteConfirmName("");
              }}
              disabled={isDeleting}
            >
              取消
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeleteInfo}
              disabled={isDeleting}
              className="gap-2"
            >
              <Trash2 className="w-4 h-4" />
              {isDeleting ? "删除中..." : "确认删除"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}

"use client"

import { UsersSkeleton } from "@/components/skeletons/users-skeleton"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import {
  createUserWithDefaultConfig,
  deleteUser,
  findAllUsers,
  updateUser,
} from "@/service/user"
import { Suspense, useCallback, useEffect, useState } from "react"
import { toast } from "sonner"
import AddUserDialog from "./add-user-dialog"
import EmployeeNumberBindingDialog from "./employee-number-binding-dialog"
import ImportExportDialog from "./import-export-dialog"
import SearchBar from "./search-bar"
import type { CreateUserFormValues } from "./user-form"
import type { UserForm } from "./user-table"
import UserTable from "./user-table"

// 用户管理内容组件
function UsersContent() {
  const [users, setUsers] = useState<UserForm[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")

  // 获取用户列表
  const fetchUsers = useCallback(async () => {
    setLoading(true)
    try {
      const usersData = await findAllUsers()
      setUsers(usersData)
    } catch (error) {
      console.error("获取用户列表失败:", error)
      toast.error("获取用户列表失败")
    } finally {
      setLoading(false)
    }
  }, [])

  useEffect(() => {
    fetchUsers()
  }, [fetchUsers])

  // 创建用户
  const handleAddUser = async (data: CreateUserFormValues) => {
    try {
      // 如果没有提供登录手机号，则使用名片手机号
      const loginPhone = data.phone || data.cardInfo.mobile

      // 创建用户并自动创建默认名片配置
      await createUserWithDefaultConfig({
        name: data.cardInfo.name, // 使用名片信息中的姓名
        phone: loginPhone,
        employeeNumber: data.employeeNumber,
        cardInfo: {
          name: data.cardInfo.name,
          email: data.cardInfo.email || null,
          department: data.cardInfo.department || null,
          position: data.cardInfo.position || null,
          title: data.cardInfo.title || null,
          company: data.cardInfo.company || null,
          mobile: data.cardInfo.mobile,
          telephone: data.cardInfo.telephone || null,
          address: data.cardInfo.address || null,
          avatar: data.cardInfo.avatar || null,
        },
      })

      toast.success("用户创建成功")
      fetchUsers() // 刷新用户列表
    } catch (error) {
      console.error("创建用户失败:", error)
      const errorMessage = error instanceof Error ? error.message : "未知错误"
      toast.error(`创建用户失败: ${errorMessage}`)
      throw error // 重新抛出错误，让组件处理
    }
  }

  // 删除用户
  const handleDeleteUser = async (userId: string) => {
    if (!confirm("确定要删除此用户吗？此操作不可撤销。")) {
      return
    }

    try {
      // 删除用户（关联的名片信息会通过外键级联删除）
      await deleteUser(userId)
      toast.success("用户删除成功")
      fetchUsers() // 刷新用户列表
    } catch (error) {
      console.error("删除用户失败:", error)
      toast.error("删除用户失败")
    }
  }

  // 编辑用户
  const handleEditUser = async (userId: string, data: CreateUserFormValues) => {
    try {
      // 处理登录手机号的逻辑
      let loginPhone = data.phone

      // 情况2：如果没有提供登录手机号，但有名片手机号，则使用名片手机号作为登录手机号
      if (!loginPhone && data.cardInfo.mobile) {
        loginPhone = data.cardInfo.mobile
        console.log(`用户 ${userId} 没有设置登录手机号，使用名片手机号作为登录手机号: ${loginPhone}`)
      }

      // 情况3：如果登录手机号被明确清空（空字符串），则传递空字符串让后端处理回落逻辑
      if (data.phone === "") {
        loginPhone = ""
        console.log(`用户 ${userId} 登录手机号被清空，将回落到名片手机号`)
      }

      // 更新用户信息
      await updateUser(userId, {
        name: data.cardInfo.name, // 使用名片信息中的姓名
        phone: loginPhone,
        employeeNumber: data.employeeNumber,
        cardInfo: {
          name: data.cardInfo.name,
          email: data.cardInfo.email || null,
          department: data.cardInfo.department || null,
          position: data.cardInfo.position || null,
          title: data.cardInfo.title || null,
          company: data.cardInfo.company || null,
          mobile: data.cardInfo.mobile,
          telephone: data.cardInfo.telephone || null,
          address: data.cardInfo.address || null,
          avatar: data.cardInfo.avatar || null,
        },
      })

      toast.success("用户更新成功")
      fetchUsers() // 刷新用户列表
    } catch (error) {
      console.error("更新用户失败:", error)
      const errorMessage = error instanceof Error ? error.message : "未知错误"
      toast.error(`更新用户失败: ${errorMessage}`)
      throw error // 重新抛出错误，让组件处理
    }
  }

  if (loading) {
    return <UsersSkeleton />
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="font-bold text-3xl">用户管理</h1>
          <p className="mt-1 text-gray-500">查看和管理系统用户</p>
        </div>
        <div className="flex items-center space-x-2">
          <EmployeeNumberBindingDialog onBindingComplete={fetchUsers} />
          <ImportExportDialog onImportComplete={fetchUsers} />
          <AddUserDialog onAddUser={handleAddUser} />
        </div>
      </div>

      {/* 搜索控制 */}
      <div className="space-y-4">
        <SearchBar searchTerm={searchTerm} setSearchTerm={setSearchTerm} />
      </div>

      <Card>
        <CardHeader>
          <CardTitle>用户列表</CardTitle>
        </CardHeader>
        <CardContent>
          <UserTable
            users={users}
            searchTerm={searchTerm}
            onDeleteUser={handleDeleteUser}
            onEditUser={handleEditUser}
          />
        </CardContent>
      </Card>
    </div>
  )
}

// 主页面组件
export default function UsersPage() {
  return (
    <Suspense fallback={<UsersSkeleton />}>
      <UsersContent />
    </Suspense>
  )
}

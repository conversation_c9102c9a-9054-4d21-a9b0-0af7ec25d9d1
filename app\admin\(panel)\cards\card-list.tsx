"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import type { CardInfo } from "@/database"
import { Eye } from "lucide-react"
import Link from "next/link"

interface CardListProps {
  cards: CardInfo[]
  searchTerm: string
}

export default function CardList({ cards, searchTerm }: CardListProps) {
  // 过滤名片
  const filteredCards = cards.filter(
    (card) =>
      card.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      card.company?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      card.mobile?.includes(searchTerm) ||
      card.email?.toLowerCase().includes(searchTerm.toLowerCase())
  )

  return (
    <Card>
      <CardHeader>
        <CardTitle>名片列表</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>姓名</TableHead>
                <TableHead>公司</TableHead>
                <TableHead>职务/职位</TableHead>
                <TableHead>手机号</TableHead>
                <TableHead>关联用户</TableHead>
                <TableHead>操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredCards.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={6} className="h-24 text-center">
                    没有找到名片
                  </TableCell>
                </TableRow>
              ) : (
                filteredCards.map((card) => (
                  <TableRow key={card.id}>
                    <TableCell>{card.name}</TableCell>
                    <TableCell>{card.company || "-"}</TableCell>
                    <TableCell>
                      {card.position || card.title
                        ? `${card.position ? card.position : ""}${
                            card.position && card.title ? "，" : ""
                          }${card.title ? card.title : ""}${
                            (card.position || card.title) && card.department ? `，${card.department}` : ""
                          }`
                        : "-"}
                    </TableCell>
                    <TableCell>{card.mobile || "-"}</TableCell>
                    <TableCell>
                      <div className="flex space-x-2">
                        <Button variant="outline" size="sm" asChild>
                          <Link
                            href={`/admin/cards/${card.id}`}
                            className="flex items-center"
                          >
                            <Eye className="mr-1 h-4 w-4" />
                            查看详情
                          </Link>
                        </Button>
                        {/* {card.userId && (
                          <Button variant="outline" size="sm" asChild>
                            <Link
                              href={`/admin/users/${card.userId}`}
                              className="flex items-center"
                            >
                              <User className="mr-1 h-4 w-4" />
                              查看用户
                            </Link>
                          </Button>
                        )} */}
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>
  )
}

"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"

import { Card, CardContent } from "@/components/ui/card"
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import {
  AlertCircle,
  ChevronDown,
  ChevronRight,
  Plus,
  Edit,
  X,
  RotateCcw,
  ExternalLink
} from "lucide-react"
import React, { useState } from "react"
import type { UserUpdateItem } from "@/service/import-utils"

// 可重用的文本组件，支持溢出时显示tooltip
function TruncatedText({ text, className = "" }: { text: string | null | undefined; className?: string }) {
  if (!text) return <span className={`text-muted-foreground ${className}`}>-</span>

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <div className={`truncate cursor-help min-w-0 ${className}`} title={text}>
            {text}
          </div>
        </TooltipTrigger>
        <TooltipContent>
          <p className="max-w-xs break-words">{text}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  )
}

interface UpdateItemsTableProps {
  items: UserUpdateItem[]
  onAbandonItem: (itemId: string) => void
}

export function UpdateItemsTable({
  items,
  onAbandonItem
}: UpdateItemsTableProps) {
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set())
  const [showNoChanges, setShowNoChanges] = useState(false)

  const toggleExpanded = (itemId: string) => {
    setExpandedItems(prev => {
      const newSet = new Set(prev)
      if (newSet.has(itemId)) {
        newSet.delete(itemId)
      } else {
        newSet.add(itemId)
      }
      return newSet
    })
  }

  const getStatusBadge = (item: UserUpdateItem) => {
    if (item.error) {
      return <Badge variant="destructive">错误</Badge>
    }

    if (item.abandoned) {
      return <Badge variant="secondary">已放弃</Badge>
    }

    if (item.type === 'create') {
      return <Badge variant="default">创建新用户</Badge>
    }

    return <Badge variant="outline">更新用户</Badge>
  }

  const getActionIcon = (item: UserUpdateItem) => {
    if (item.type === 'create') {
      return <Plus className="h-4 w-4" />
    }
    return <Edit className="h-4 w-4" />
  }

  const hasChanges = React.useCallback((item: UserUpdateItem) => {
    return item.changes.some(change => change.isChanged)
  }, [])

  // 按修改状态和错误状态分组并排序
  const groupedItems = React.useMemo(() => {
    // 错误行
    const errorItems = items.filter(item => item.error)
    // 有修改的行（非错误）
    const changedItems = items.filter(item => !item.error && hasChanges(item))
    // 无修改的行
    const noChangeItems = items.filter(item => !item.error && !hasChanges(item))

    // 对每组按行号排序
    errorItems.sort((a, b) => a.rowIndex - b.rowIndex)
    changedItems.sort((a, b) => a.rowIndex - b.rowIndex)
    noChangeItems.sort((a, b) => a.rowIndex - b.rowIndex)

    return {
      errorItems,
      changedItems,
      noChangeItems
    }
  }, [items, hasChanges])

  // 渲染项目行的函数
  const renderItemRow = (item: UserUpdateItem) => (
    <React.Fragment key={item.id}>
      <TableRow
        className={`
          ${item.abandoned ? "opacity-50" : ""}
          ${item.error ? "border-l-4 border-l-red-500 bg-red-50" : ""}
        `.trim()}
      >
        <TableCell>
          <Button
            variant="ghost"
            size="sm"
            className="p-0 h-6 w-6"
            onClick={() => toggleExpanded(item.id)}
          >
            {expandedItems.has(item.id) ? (
              <ChevronDown className="h-4 w-4" />
            ) : (
              <ChevronRight className="h-4 w-4" />
            )}
          </Button>
        </TableCell>
        <TableCell className="font-mono text-sm">
          {item.rowIndex + 2}
        </TableCell>
        <TableCell className="font-medium">
          {item.importData.姓名}
        </TableCell>
        <TableCell>
          <div className="flex items-center space-x-2">
            {getActionIcon(item)}
            <span>
              {item.type === 'create' ? '创建新用户' : '更新用户'}
            </span>
          </div>
        </TableCell>
        <TableCell>
          {getStatusBadge(item)}
        </TableCell>
        <TableCell>
          <div className="flex items-center space-x-2">
            <span className={hasChanges(item) ? "text-orange-600 font-medium" : "text-muted-foreground"}>
              {item.changes.filter(c => c.isChanged).length}
            </span>
            {hasChanges(item) && (
              <Badge variant="outline">有变更</Badge>
            )}
          </div>
        </TableCell>
        <TableCell className="text-right">
          {!item.abandoned && !item.error && (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => onAbandonItem(item.id)}
                    className="h-8 w-8 p-0"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>放弃此项更新</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}
          {item.abandoned && (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => onAbandonItem(item.id)}
                    className="h-8 w-8 p-0"
                  >
                    <RotateCcw className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>恢复此项更新</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}
        </TableCell>
      </TableRow>

      {expandedItems.has(item.id) && (
        <TableRow>
          <TableCell colSpan={7} className="p-0">
            <div className="border-t bg-muted/30 p-4">
              <div className="space-y-4">
                {/* 错误信息显示 */}
                {item.error && (
                  <div className="bg-red-50 border border-red-200 rounded p-3">
                    <div className="flex items-start space-x-2">
                      <AlertCircle className="h-4 w-4 text-red-500 mt-0.5 flex-shrink-0" />
                      <div>
                        <h4 className="font-medium text-red-700 mb-1">错误信息</h4>
                        <p className="text-sm text-red-600">{item.error}</p>
                      </div>
                    </div>
                  </div>
                )}

                {/* 导入数据预览 */}
                <div>
                  <h4 className="font-medium mb-2">导入数据</h4>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-sm">
                    <div>
                      <span className="text-muted-foreground">工号:</span>
                      <span className="ml-2 font-medium">{item.importData.工号}</span>
                    </div>
                    <div>
                      <span className="text-muted-foreground">登录手机号:</span>
                      <span className="ml-2">{item.importData.登录手机号 || '-'}</span>
                    </div>
                    <div>
                      <span className="text-muted-foreground">公司:</span>
                      <span className="ml-2">{item.importData.公司 || '-'}</span>
                    </div>
                    <div>
                      <span className="text-muted-foreground">部门:</span>
                      <span className="ml-2">{item.importData.部门 || '-'}</span>
                    </div>
                    <div>
                      <span className="text-muted-foreground">职务:</span>
                      <span className="ml-2">{item.importData.职务 || '-'}</span>
                    </div>
                    <div>
                      <span className="text-muted-foreground">职位:</span>
                      <span className="ml-2">{item.importData.职位 || '-'}</span>
                    </div>
                    <div>
                      <span className="text-muted-foreground">手机号:</span>
                      <span className="ml-2">{item.importData.手机号 || '-'}</span>
                    </div>
                    <div>
                      <span className="text-muted-foreground">座机:</span>
                      <span className="ml-2">{item.importData.座机 || '-'}</span>
                    </div>
                    <div>
                      <span className="text-muted-foreground">邮箱:</span>
                      <span className="ml-2">{item.importData.邮箱 || '-'}</span>
                    </div>
                    <div>
                      <span className="text-muted-foreground">地址:</span>
                      <span className="ml-2">{item.importData.地址 || '-'}</span>
                    </div>
                    <div>
                      <span className="text-muted-foreground">头像URL:</span>
                      <span className="ml-2">{item.importData.头像URL || '-'}</span>
                    </div>
                  </div>
                </div>

                {/* 目标用户信息 */}
                {item.targetUser && (
                  <div>
                    <h4 className="font-medium mb-2 flex items-center">
                      目标用户
                      <Button variant="ghost" size="sm" className="ml-2 h-6 px-2">
                        <ExternalLink className="h-3 w-3" />
                      </Button>
                    </h4>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-sm">
                      <div className="min-w-0">
                        <span className="text-muted-foreground">工号:</span>
                        <div className="ml-2">
                          <TruncatedText text={item.targetUser.employeeNumber} className="font-medium" />
                        </div>
                      </div>
                      <div className="min-w-0">
                        <span className="text-muted-foreground">当前姓名:</span>
                        <div className="ml-2">
                          <TruncatedText text={item.targetUser.name} />
                        </div>
                      </div>
                      <div className="min-w-0">
                        <span className="text-muted-foreground">当前手机号:</span>
                        <div className="ml-2">
                          <TruncatedText text={item.targetUser.username} />
                        </div>
                      </div>
                      <div className="min-w-0">
                        <span className="text-muted-foreground">创建时间:</span>
                        <span className="ml-2 truncate">{new Date(item.targetUser.createdAt).toLocaleDateString()}</span>
                      </div>
                    </div>
                  </div>
                )}

                {/* 字段变更详情 */}
                {item.changes.length > 0 && (
                  <div>
                    <h4 className="font-medium mb-2">字段变更</h4>
                    <div className="space-y-2">
                      {item.changes.map((change) => (
                        <div
                          key={change.field}
                          className={`flex items-center justify-between p-2 rounded border ${
                            change.isChanged
                              ? 'bg-orange-50 border-orange-200'
                              : 'bg-gray-50 border-gray-200'
                          }`}
                        >
                          <div className="flex items-center space-x-4">
                            <span className="font-medium text-sm min-w-[80px]">
                              {change.fieldLabel}
                            </span>
                            <div className="flex items-center space-x-2 text-sm">
                              <span className="text-muted-foreground">
                                {change.oldValue || '(空)'}
                              </span>
                              <span className="text-muted-foreground">→</span>
                              <span className={change.isChanged ? "font-medium" : "text-muted-foreground"}>
                                {change.newValue || '(空)'}
                              </span>
                            </div>
                          </div>
                          {change.isChanged && (
                            <Badge variant="outline">变更</Badge>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </TableCell>
        </TableRow>
      )}
    </React.Fragment>
  )

  return (
    <Card>
      <CardContent className="p-0">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-12" />
              <TableHead>行号</TableHead>
              <TableHead>姓名</TableHead>
              <TableHead>操作类型</TableHead>
              <TableHead>状态</TableHead>
              <TableHead>变更数量</TableHead>
              <TableHead className="text-right">操作</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {/* 错误项目 - 排在最前面 */}
            {groupedItems.errorItems.map((item) => renderItemRow(item))}

            {/* 有变更的项目 - 按行号排序 */}
            {groupedItems.changedItems.map((item) => renderItemRow(item))}

            {/* 无修改的项目分组 - 折叠显示 */}
            {groupedItems.noChangeItems.length > 0 && (
              <>
                <TableRow className="bg-gray-50">
                  <TableCell colSpan={7} className="py-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setShowNoChanges(!showNoChanges)}
                      className="flex items-center space-x-2 text-muted-foreground hover:text-foreground"
                    >
                      {showNoChanges ? (
                        <ChevronDown className="h-4 w-4" />
                      ) : (
                        <ChevronRight className="h-4 w-4" />
                      )}
                      <span>无修改项目 ({groupedItems.noChangeItems.length})</span>
                    </Button>
                  </TableCell>
                </TableRow>
                {showNoChanges && groupedItems.noChangeItems.map((item) => renderItemRow(item))}
              </>
            )}

            {/* 原有的渲染逻辑作为备用 */}
            {false && items.map((item) => (
              <React.Fragment key={item.id}>
                <TableRow
                  className={`
                    ${item.abandoned ? "opacity-50" : ""}
                    ${item.error ? "border-l-4 border-l-red-500 bg-red-50" : ""}
                  `.trim()}
                >
                  <TableCell>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="p-0 h-6 w-6"
                      onClick={() => toggleExpanded(item.id)}
                    >
                      {expandedItems.has(item.id) ? (
                        <ChevronDown className="h-4 w-4" />
                      ) : (
                        <ChevronRight className="h-4 w-4" />
                      )}
                    </Button>
                  </TableCell>
                    <TableCell className="font-mono text-sm">
                      {item.rowIndex + 2}
                    </TableCell>
                    <TableCell className="font-medium">
                      {item.importData.姓名}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        {getActionIcon(item)}
                        <span>
                          {item.type === 'create' ? '创建新用户' : '更新用户'}
                        </span>
                      </div>
                    </TableCell>
                    <TableCell>
                      {getStatusBadge(item)}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        <span className={hasChanges(item) ? "text-orange-600 font-medium" : "text-muted-foreground"}>
                          {item.changes.filter(c => c.isChanged).length}
                        </span>
                        {hasChanges(item) && (
                          <Badge variant="outline">有变更</Badge>
                        )}
                      </div>
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex items-center justify-end space-x-2">
                        <Button
                          variant={item.abandoned ? "outline" : "ghost"}
                          size="sm"
                          onClick={() => onAbandonItem(item.id)}
                        >
                          {item.abandoned ? (
                            <>
                              <RotateCcw className="h-4 w-4 mr-1" />
                              恢复
                            </>
                          ) : (
                            <>
                              <X className="h-4 w-4 mr-1" />
                              放弃
                            </>
                          )}
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>

                  {expandedItems.has(item.id) && (
                    <TableRow>
                      <TableCell colSpan={7} className="p-0">
                        <div className="border-t bg-muted/30 p-4">
                          <div className="space-y-4">
                            {/* 错误信息显示 */}
                            {item.error && (
                              <div className="bg-red-50 border border-red-200 rounded p-3">
                                <div className="flex items-start space-x-2">
                                  <AlertCircle className="h-4 w-4 text-red-500 mt-0.5 flex-shrink-0" />
                                  <div>
                                    <h4 className="font-medium text-red-700 mb-1">错误信息</h4>
                                    <p className="text-sm text-red-600">{item.error}</p>
                                  </div>
                                </div>
                              </div>
                            )}

                            {/* 导入数据预览 */}
                            <div>
                              <h4 className="font-medium mb-2">导入数据</h4>
                              <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-sm">
                                <div>
                                  <span className="text-muted-foreground">工号:</span>
                                  <span className="ml-2 font-medium">{item.importData.工号}</span>
                                </div>
                                <div>
                                  <span className="text-muted-foreground">登录手机号:</span>
                                  <span className="ml-2">{item.importData.登录手机号 || '-'}</span>
                                </div>
                                <div>
                                  <span className="text-muted-foreground">公司:</span>
                                  <span className="ml-2">{item.importData.公司 || '-'}</span>
                                </div>
                                <div>
                                  <span className="text-muted-foreground">部门:</span>
                                  <span className="ml-2">{item.importData.部门 || '-'}</span>
                                </div>
                                <div>
                                  <span className="text-muted-foreground">职务:</span>
                                  <span className="ml-2">{item.importData.职务 || '-'}</span>
                                </div>
                                <div>
                                  <span className="text-muted-foreground">职位:</span>
                                  <span className="ml-2">{item.importData.职位 || '-'}</span>
                                </div>
                                <div>
                                  <span className="text-muted-foreground">手机号:</span>
                                  <span className="ml-2">{item.importData.手机号 || '-'}</span>
                                </div>
                                <div>
                                  <span className="text-muted-foreground">座机:</span>
                                  <span className="ml-2">{item.importData.座机 || '-'}</span>
                                </div>
                                <div>
                                  <span className="text-muted-foreground">邮箱:</span>
                                  <span className="ml-2">{item.importData.邮箱 || '-'}</span>
                                </div>
                                <div>
                                  <span className="text-muted-foreground">地址:</span>
                                  <span className="ml-2">{item.importData.地址 || '-'}</span>
                                </div>
                                <div>
                                  <span className="text-muted-foreground">头像URL:</span>
                                  <span className="ml-2">{item.importData.头像URL || '-'}</span>
                                </div>
                              </div>
                            </div>

                            {/* 目标用户信息 */}
                            {item.targetUser && (
                              <div>
                                <h4 className="font-medium mb-2 flex items-center">
                                  目标用户
                                  <Button variant="ghost" size="sm" className="ml-2 h-6 px-2">
                                    <ExternalLink className="h-3 w-3" />
                                  </Button>
                                </h4>
                                <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-sm">
                                  <div className="min-w-0">
                                    <span className="text-muted-foreground">工号:</span>
                                    <div className="ml-2">
                                      <TruncatedText text={item.targetUser.employeeNumber} className="font-medium" />
                                    </div>
                                  </div>
                                  <div className="min-w-0">
                                    <span className="text-muted-foreground">当前姓名:</span>
                                    <div className="ml-2">
                                      <TruncatedText text={item.targetUser.name} />
                                    </div>
                                  </div>
                                  <div className="min-w-0">
                                    <span className="text-muted-foreground">当前手机号:</span>
                                    <div className="ml-2">
                                      <TruncatedText text={item.targetUser.username} />
                                    </div>
                                  </div>
                                  <div className="min-w-0">
                                    <span className="text-muted-foreground">创建时间:</span>
                                    <span className="ml-2 truncate">{new Date(item.targetUser.createdAt).toLocaleDateString()}</span>
                                  </div>
                                </div>
                              </div>
                            )}

                            {/* 字段变更详情 */}
                            {item.changes.length > 0 && (
                              <div>
                                <h4 className="font-medium mb-2">字段变更</h4>
                                <div className="space-y-2">
                                  {item.changes.map((change) => (
                                    <div
                                      key={change.field}
                                      className={`flex items-center justify-between p-2 rounded border ${
                                        change.isChanged
                                          ? 'bg-orange-50 border-orange-200'
                                          : 'bg-gray-50 border-gray-200'
                                      }`}
                                    >
                                      <div className="flex items-center space-x-4">
                                        <span className="font-medium text-sm min-w-[80px]">
                                          {change.fieldLabel}
                                        </span>
                                        <div className="flex items-center space-x-2 text-sm">
                                          <span className="text-muted-foreground">
                                            {change.oldValue || '(空)'}
                                          </span>
                                          <span className="text-muted-foreground">→</span>
                                          <span className={change.isChanged ? "font-medium" : "text-muted-foreground"}>
                                            {change.newValue || '(空)'}
                                          </span>
                                        </div>
                                      </div>
                                      {change.isChanged && (
                                        <Badge variant="outline">变更</Badge>
                                      )}
                                    </div>
                                  ))}
                                </div>
                              </div>
                            )}
                          </div>
                        </div>
                      </TableCell>
                    </TableRow>
                  )}
                </React.Fragment>
            ))}
            {/* 结束原有渲染逻辑 */}
          </TableBody>
        </Table>

        {items.length === 0 && (
          <div className="text-center py-8 text-muted-foreground">
            没有找到更新项目
          </div>
        )}
      </CardContent>
    </Card>
  )
}

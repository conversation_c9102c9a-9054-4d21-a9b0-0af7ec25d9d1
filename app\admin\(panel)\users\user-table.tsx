"use client"

import { But<PERSON> } from "@/components/ui/button"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import type { CardInfo, User } from "@/database"
import { getProcessedImageUrl } from "@/lib/imageUtils"
import {
  type ColumnDef,
  type ColumnFiltersState,
  type SortingState,
  type Column,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table"
import { ArrowUpDown, ArrowUp, ArrowDown, Trash2 } from "lucide-react"
import { useState } from "react"
import EditUserDialog from "./edit-user-dialog"
import type { CreateUserFormValues } from "./user-form"
import ViewCardDialog from "./view-card-dialog"

// 用户类型定义
export type UserForm = {
  user: User  // 保留完整的用户数据，包括时间戳
  cardInfo: CardInfo | null  // 保留完整的名片数据，包括时间戳
}

interface UserTableProps {
  users: UserForm[]
  searchTerm: string
  onDeleteUser: (userId: string) => Promise<void>
  onEditUser: (userId: string, data: CreateUserFormValues) => Promise<void>
}

export default function UserTable({
  users,
  searchTerm,
  onDeleteUser,
  onEditUser,
}: UserTableProps) {
  // 默认按最后修改时间降序排序（最新的在前面）
  const [sorting, setSorting] = useState<SortingState>([
    { id: "updatedAt", desc: true }
  ])
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([])

  // 排序按钮组件
  const SortButton = ({ column, children }: { column: Column<UserForm, unknown>; children: React.ReactNode }) => {
    return (
      <Button
        variant="ghost"
        onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        className="h-auto p-0 font-medium hover:bg-transparent"
      >
        {children}
        {column.getIsSorted() === "asc" ? (
          <ArrowUp className="ml-2 h-4 w-4" />
        ) : column.getIsSorted() === "desc" ? (
          <ArrowDown className="ml-2 h-4 w-4" />
        ) : (
          <ArrowUpDown className="ml-2 h-4 w-4" />
        )}
      </Button>
    )
  }

  // 表格列定义
  const columns: ColumnDef<UserForm>[] = [
    {
      id: "username",
      accessorFn: (row) => row.user.username,
      header: ({ column }) => (
        <SortButton column={column}>登录手机号</SortButton>
      ),
      cell: ({ row }) => <div>{row.original.user.username}</div>,
    },
    {
      id: "name",
      accessorFn: (row) => row.user.name,
      header: ({ column }) => (
        <SortButton column={column}>姓名</SortButton>
      ),
      cell: ({ row }) => <div>{row.original.user.name}</div>,
    },
    {
      id: "employeeNumber",
      accessorFn: (row) => row.user.employeeNumber || "",
      header: ({ column }) => (
        <SortButton column={column}>工号</SortButton>
      ),
      cell: ({ row }) => (
        <div>{row.original.user.employeeNumber || "-"}</div>
      ),
    },
    {
      id: "updatedAt",
      accessorFn: (row) => row.user.updatedAt,
      header: ({ column }) => (
        <SortButton column={column}>最后修改时间</SortButton>
      ),
      cell: ({ row }) => (
        <div>
          {row.original.user.updatedAt
            ? new Date(row.original.user.updatedAt).toLocaleDateString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit'
              })
            : "-"
          }
        </div>
      ),
    },
    {
      accessorKey: "cardInfo",
      header: "名片",
      enableSorting: false,
      cell: ({ row }) => (
        <div className="flex items-center">
          {row.original.cardInfo ? (
            <>
              {row.original.cardInfo.avatar && (
                <div className="relative mr-2 h-8 w-8 overflow-hidden rounded-full">
                  <img
                    src={getProcessedImageUrl(row.original.cardInfo.avatar, {
                      width: 100,
                      height: 100,
                      format: "webp",
                    })}
                    alt="头像"
                    className="h-full w-full object-cover"
                    onError={(e) => {
                      e.currentTarget.style.display = "none"
                    }}
                  />
                </div>
              )}
              <ViewCardDialog user={row.original} />
            </>
          ) : (
            <span className="text-gray-400">无名片</span>
          )}
        </div>
      ),
    },
    {
      id: "actions",
      header: "操作",
      enableSorting: false,
      cell: ({ row }) => (
        <div className="flex space-x-2">
          <EditUserDialog user={row.original} onEditUser={onEditUser} />
          <Button
            variant="outline"
            size="sm"
            onClick={() => onDeleteUser(row.original.user.id)}
            className="flex items-center"
          >
            <Trash2 className="mr-1 h-4 w-4" />
            删除
          </Button>
        </div>
      ),
    },
  ]

  // 初始化表格
  const table = useReactTable({
    data: users,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    state: {
      sorting,
      columnFilters,
      globalFilter: searchTerm,
    },
    onGlobalFilterChange: () => {},
  })

  return (
    <>
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <TableHead key={header.id}>
                    {header.isPlaceholder
                      ? null
                      : flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  没有找到用户
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      <div className="flex items-center justify-end space-x-2 py-4">
        <Button
          variant="outline"
          size="sm"
          onClick={() => table.previousPage()}
          disabled={!table.getCanPreviousPage()}
        >
          上一页
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={() => table.nextPage()}
          disabled={!table.getCanNextPage()}
        >
          下一页
        </Button>
      </div>
    </>
  )
}

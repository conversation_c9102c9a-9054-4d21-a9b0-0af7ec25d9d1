"use server";

import { info, db } from "@/database";
import { eq, desc } from "drizzle-orm";

export async function saveInfo(name: string, content: string, contentType: string) {
  try {
    if (!name?.trim()) {
      return { error: "名称不能为空" };
    }

    if (!content?.trim()) {
      return { error: "内容不能为空" };
    }

    const trimmedName = name.trim();
    const meta = { type: contentType };

    // 检查是否已存在
    const [existing] = await db.select().from(info).where(eq(info.name, trimmedName)).limit(1);

    const now = new Date();

    if (existing) {
      // 更新现有记录
      await db
        .update(info)
        .set({
          content,
          meta,
          updatedAt: now,
        })
        .where(eq(info.name, trimmedName));

      console.log(`[Info] 更新成功, 名称: ${trimmedName}`);
    } else {
      // 创建新记录
      const id = `info_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;

      await db.insert(info).values({
        id,
        name: trimmedName,
        content,
        meta,
        createdAt: now,
        updatedAt: now,
      });

      console.log(`[Info] 创建成功, 名称: ${trimmedName}, ID: ${id}`);
    }

    return { success: true };
  } catch (error) {
    console.error("[Info] 保存失败:", error);
    return { error: "保存失败" };
  }
}

export async function getInfoList() {
  try {
    const results = await db
      .select({
        id: info.id,
        name: info.name,
        content: info.content,
        meta: info.meta,
        createdAt: info.createdAt,
        updatedAt: info.updatedAt,
      })
      .from(info)
      .orderBy(desc(info.updatedAt));

    return { data: results };
  } catch (error) {
    console.error("[Info] 获取列表失败:", error);
    return { error: "获取列表失败" };
  }
}

export async function getInfo(name: string) {
  try {
    if (!name) {
      return { error: "名称不能为空" };
    }

    const [result] = await db.select().from(info).where(eq(info.name, name)).limit(1);

    if (!result) {
      return { error: "未找到信息" };
    }

    const metadata = result.meta as Record<string, unknown> || {};

    return {
      data: {
        content: result.content,
        meta: metadata
      }
    };
  } catch (error) {
    console.error("[Info] 获取信息失败:", error);
    return { error: "获取信息失败" };
  }
}

export async function deleteInfo(name: string) {
  try {
    if (!name?.trim()) {
      return { error: "名称不能为空" };
    }

    const trimmedName = name.trim();

    // 检查是否存在
    const [existing] = await db.select().from(info).where(eq(info.name, trimmedName)).limit(1);

    if (!existing) {
      return { error: "未找到要删除的信息" };
    }

    // 删除记录
    await db.delete(info).where(eq(info.name, trimmedName));

    console.log(`[Info] 删除成功, 名称: ${trimmedName}`);
    return { success: true };
  } catch (error) {
    console.error("[Info] 删除失败:", error);
    return { error: "删除失败" };
  }
} 
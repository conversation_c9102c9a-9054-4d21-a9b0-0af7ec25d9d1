"use client"

import InfoAlert from "@/components/info-alert"
import { CardsSkeleton } from "@/components/skeletons/cards-skeleton"
import type { CardInfo } from "@/database"
import { findAllCardInfo } from "@/service/cardInfo"
import { Suspense, useCallback, useEffect, useState } from "react"
import { toast } from "sonner"
import CardList from "./card-list"
import SearchBar from "./search-bar"

// 名片管理内容组件
function CardsContent() {
  const [cards, setCards] = useState<CardInfo[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")

  // 获取名片列表
  const fetchCards = useCallback(async () => {
    setLoading(true)
    try {
      const cardsData = await findAllCardInfo()
      setCards(cardsData)
    } catch (error) {
      console.error("获取名片列表失败:", error)
      toast.error("获取名片列表失败")
    } finally {
      setLoading(false)
    }
  }, [])

  useEffect(() => {
    fetchCards()
  }, [fetchCards])

  if (loading) {
    return <CardsSkeleton />
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="font-bold text-3xl">名片管理</h1>
          <p className="mt-1 text-gray-500">查看用户名片信息</p>
        </div>
      </div>

      <InfoAlert title="注意">
        管理员可以查看名片信息和配置，但只能设置配置是否公开。名片的编辑和配置创建功能只能在小程序端进行。
      </InfoAlert>

      <SearchBar searchTerm={searchTerm} setSearchTerm={setSearchTerm} />

      <CardList cards={cards} searchTerm={searchTerm} />
    </div>
  )
}

// 主页面组件
export default function CardsPage() {
  return (
    <Suspense fallback={<CardsSkeleton />}>
      <CardsContent />
    </Suspense>
  )
}

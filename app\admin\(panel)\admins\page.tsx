"use client"

import { UsersSkeleton } from "@/components/skeletons/users-skeleton"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import type { User } from "@/database"
import { findAllAdmins } from "@/service/admin"
import { Suspense, useCallback, useEffect, useState } from "react"
import { Toaster, toast } from "sonner"
import AdminTable from "./admin-table"
import SearchBar from "./search-bar"

// 管理员管理内容组件
function AdminsContent() {
  const [admins, setAdmins] = useState<User[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")

  // 获取管理员列表
  const fetchAdmins = useCallback(async () => {
    setLoading(true)
    try {
      const adminsData = await findAllAdmins()
      setAdmins(adminsData)
    } catch (error) {
      console.error("获取管理员列表失败:", error)
      toast.error("获取管理员列表失败")
    } finally {
      setLoading(false)
    }
  }, [])

  useEffect(() => {
    fetchAdmins()
  }, [fetchAdmins])

  if (loading) {
    return <UsersSkeleton />
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="font-bold text-3xl">管理员管理</h1>
        <p className="mt-1 text-gray-500">
          查看和管理系统管理员账户，目前仅支持修改管理员密码
        </p>
      </div>

      <SearchBar searchTerm={searchTerm} setSearchTerm={setSearchTerm} />

      <Card>
        <CardHeader>
          <CardTitle>管理员列表</CardTitle>
        </CardHeader>
        <CardContent>
          <AdminTable admins={admins} searchTerm={searchTerm} />
        </CardContent>
      </Card>
    </div>
  )
}

// 主页面组件
export default function AdminsPage() {
  return (
    <Suspense fallback={<UsersSkeleton />}>
      <AdminsContent />
      <Toaster />
    </Suspense>
  )
}

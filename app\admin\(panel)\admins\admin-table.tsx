"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import type { User } from "@/database"
import { Key } from "lucide-react"
import { useState } from "react"
import ChangePasswordDialog from "./change-password-dialog"

interface AdminTableProps {
  admins: User[]
  searchTerm?: string
}

export default function AdminTable({
  admins,
  searchTerm = "",
}: AdminTableProps) {
  const [selectedAdmin, setSelectedAdmin] = useState<User | null>(null)
  const [isChangePasswordOpen, setIsChangePasswordOpen] = useState(false)

  // 过滤管理员列表
  const filteredAdmins = admins.filter((admin) => {
    const searchLower = searchTerm.toLowerCase()
    return (
      admin.name.toLowerCase().includes(searchLower) ||
      admin.username?.toLowerCase().includes(searchLower) ||
      admin.email.toLowerCase().includes(searchLower)
    )
  })

  // 打开修改密码对话框
  const handleChangePassword = (admin: User) => {
    setSelectedAdmin(admin)
    setIsChangePasswordOpen(true)
  }

  // 关闭修改密码对话框
  const handleCloseChangePassword = () => {
    setIsChangePasswordOpen(false)
    setSelectedAdmin(null)
  }

  return (
    <div>
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>用户名</TableHead>
              <TableHead>昵称</TableHead>
              <TableHead>创建时间</TableHead>
              <TableHead>操作</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredAdmins.length === 0 ? (
              <TableRow>
                <TableCell colSpan={5} className="h-24 text-center">
                  没有找到管理员
                </TableCell>
              </TableRow>
            ) : (
              filteredAdmins.map((admin) => (
                <TableRow key={admin.id}>
                  <TableCell>{admin.username || "未设置"}</TableCell>
                  <TableCell>{admin.name}</TableCell>
                  <TableCell>
                    {new Date(admin.createdAt).toLocaleDateString("zh-CN", {
                      year: "numeric",
                      month: "2-digit",
                      day: "2-digit",
                    })}
                  </TableCell>
                  <TableCell>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleChangePassword(admin)}
                    >
                      <Key className="mr-2 h-4 w-4" />
                      修改密码
                    </Button>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* 修改密码对话框 */}
      {selectedAdmin && (
        <ChangePasswordDialog
          admin={selectedAdmin}
          open={isChangePasswordOpen}
          onClose={handleCloseChangePassword}
        />
      )}
    </div>
  )
}

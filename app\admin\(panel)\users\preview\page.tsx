"use client";

import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  AlertCircle,
  CheckCircle2,
  Users,
  ArrowLeft,
  UserPlus,
  UserCheck,
} from "lucide-react";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { toast } from "sonner";
import { executeImportUpdates } from "@/service/import";
import {
  type ImportAnalysisResult,
  type UserUpdateItem,
  ImportMode,
} from "@/service/import-utils";
import { UpdateItemsTable } from "./update-items-table";

export default function PreviewPage() {
  const router = useRouter();
  const [analysisResult, setAnalysisResult] =
    useState<ImportAnalysisResult | null>(null);
  const [updateItems, setUpdateItems] = useState<UserUpdateItem[]>([]);
  const [isExecuting, setIsExecuting] = useState(false);

  useEffect(() => {
    // 从 sessionStorage 获取分析结果
    const storedResult = sessionStorage.getItem("importAnalysisResult");
    if (storedResult) {
      try {
        const result: ImportAnalysisResult = JSON.parse(storedResult);
        setAnalysisResult(result);
        setUpdateItems(result.updateItems);
      } catch (error) {
        console.error("解析导入分析结果失败:", error);
        toast.error("数据加载失败，请重新导入");
        router.push("/admin/users");
      }
    } else {
      toast.error("没有找到导入数据，请重新导入");
      router.push("/admin/users");
    }
  }, [router]);

  // 处理放弃更新
  const handleAbandonItem = (itemId: string) => {
    setUpdateItems((prev) =>
      prev.map((item) =>
        item.id === itemId ? { ...item, abandoned: !item.abandoned } : item
      )
    );
  };

  // 处理批量操作
  const handleBatchAbandon = (abandon: boolean) => {
    setUpdateItems((prev) =>
      prev.map((item) => ({ ...item, abandoned: abandon }))
    );
  };

  // 执行更新
  const handleExecuteUpdates = async () => {
    if (!analysisResult) return;

    try {
      setIsExecuting(true);
      const result = await executeImportUpdates(
        updateItems,
        analysisResult.mode
      );

      if (result.success) {
        toast.success(
          `导入完成！创建 ${result.created} 个用户，更新 ${result.updated} 个用户`
        );
      } else {
        toast.warning(
          `导入部分完成：创建 ${result.created} 个，更新 ${result.updated} 个，失败 ${result.failed} 个`
        );
      }

      // 清除 sessionStorage 并返回用户列表
      sessionStorage.removeItem("importAnalysisResult");
      router.push("/admin/users");
    } catch (error) {
      console.error("执行导入失败:", error);
      toast.error(
        `导入失败: ${error instanceof Error ? error.message : "未知错误"}`
      );
    } finally {
      setIsExecuting(false);
    }
  };



  if (!analysisResult) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <Users className="mx-auto h-8 w-8 text-muted-foreground mb-2" />
          <p className="text-muted-foreground">加载导入预览...</p>
        </div>
      </div>
    );
  }

  const stats = {
    total: updateItems.length,
    updates: updateItems.filter((item) =>
      item.type === 'update' &&
      !item.abandoned &&
      !item.error &&
      item.changes.some(c => c.isChanged)
    ).length,
    creates: updateItems.filter((item) =>
      item.type === 'create' &&
      !item.abandoned &&
      !item.error
    ).length,
    noChanges: updateItems.filter((item) =>
      !item.abandoned &&
      !item.error &&
      !item.changes.some(c => c.isChanged)
    ).length,
    abandoned: updateItems.filter((item) => item.abandoned).length,
    errors: analysisResult.errors.length,
  };

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* 页面标题和返回按钮 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="ghost"
            size="lg"
            onClick={() => router.push("/admin/users")}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            取消导入并<br/>
            返回用户列表
          </Button>
          <div>
            <h1 className="text-2xl font-bold">导入预览</h1>
            <p className="text-muted-foreground">
              基于工号匹配的导入结果预览，确认无误后执行导入
            </p>
          </div>
        </div>
      </div>

      {/* 统计信息 */}
      <div className="grid grid-cols-2 md:grid-cols-6 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Users className="h-4 w-4 text-blue-500" />
              <div>
                <p className="text-sm font-medium">总计</p>
                <p className="text-2xl font-bold">{stats.total}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <UserCheck className="h-4 w-4 text-green-500" />
              <div>
                <p className="text-sm font-medium">更新用户</p>
                <p className="text-2xl font-bold">{stats.updates}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <UserPlus className="h-4 w-4 text-blue-500" />
              <div>
                <p className="text-sm font-medium">新建用户</p>
                <p className="text-2xl font-bold">{stats.creates}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <CheckCircle2 className="h-4 w-4 text-gray-500" />
              <div>
                <p className="text-sm font-medium">已放弃</p>
                <p className="text-2xl font-bold">{stats.abandoned}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <CheckCircle2 className="h-4 w-4 text-blue-400" />
              <div>
                <p className="text-sm font-medium">无修改</p>
                <p className="text-2xl font-bold">{stats.noChanges}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <AlertCircle className="h-4 w-4 text-red-500" />
              <div>
                <p className="text-sm font-medium">错误</p>
                <p className="text-2xl font-bold">{stats.errors}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>



      {/* 导入模式显示 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <span>导入设置</span>
            <Badge
              variant={
                analysisResult.mode === ImportMode.PARTIAL_UPDATE
                  ? "default"
                  : "secondary"
              }
            >
              {analysisResult.mode === ImportMode.PARTIAL_UPDATE
                ? "部分更新"
                : "完全覆盖"}
            </Badge>
          </CardTitle>
          <CardDescription>
            {analysisResult.mode === ImportMode.PARTIAL_UPDATE
              ? "只更新Excel中有内容的字段，空白字段保持原有数据不变"
              : "更新所有字段，空白字段会清空原有数据"}
          </CardDescription>
        </CardHeader>
      </Card>

      {/* 操作按钮 */}
      <div className="flex items-center justify-between">
        <div className="flex space-x-2">
          <Button variant="outline" onClick={() => handleBatchAbandon(true)}>
            全部放弃
          </Button>

          <Button variant="outline" onClick={() => handleBatchAbandon(false)}>
            全部恢复
          </Button>
        </div>

        <Button
          onClick={handleExecuteUpdates}
          disabled={isExecuting || (stats.updates + stats.creates) === 0}
          className="min-w-[120px]"
        >
          {isExecuting ? "执行中..." : `确认导入 (${stats.updates + stats.creates})`}
        </Button>
      </div>

      {/* 更新项目表格 */}
      <UpdateItemsTable
        items={updateItems}
        onAbandonItem={handleAbandonItem}
      />
    </div>
  );
}

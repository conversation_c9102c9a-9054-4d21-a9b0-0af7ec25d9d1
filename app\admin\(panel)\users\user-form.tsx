"use client"

import { zodResolver } from "@hookform/resolvers/zod"
import { useState } from "react"
import { useForm } from "react-hook-form"
import { z } from "zod"

import AvatarUrlInput from "@/components/AvatarUrlInput"
import { But<PERSON> } from "@/components/ui/button"
import { DialogFooter } from "@/components/ui/dialog"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"

// 创建用户的表单验证模式
export const createUserSchema = z.object({
  phone: z.string().optional(),
  employeeNumber: z.string().optional(),
  cardInfo: z.object({
    avatar: z.string().url("请输入有效的URL").optional().or(z.literal("")),
    name: z.string().min(1, "姓名不能为空"),
    department: z.string().optional(),
    position: z.string().optional(),
    title: z.string().optional(),
    company: z.string().optional(),
    mobile: z.string().min(1, "手机号不能为空"),
    telephone: z.string().optional(),
    email: z
      .string()
      .trim()
      .email("请输入有效的邮箱地址")
      .optional()
      .or(z.literal("")),
    address: z.string().optional(),
  }),
})

export type CreateUserFormValues = z.infer<typeof createUserSchema>

interface UserFormProps {
  onSubmit: (data: CreateUserFormValues) => Promise<void>
  isSubmitting: boolean
  defaultValues?: CreateUserFormValues
  isEditing?: boolean // 是否是编辑模式
}

export default function UserForm({
  onSubmit,
  isSubmitting,
  defaultValues,
  isEditing = false,
}: UserFormProps) {
  // 添加URL验证状态
  const [isAvatarUrlValid, setIsAvatarUrlValid] = useState(true)

  // 表单初始化
  const form = useForm<CreateUserFormValues>({
    resolver: zodResolver(createUserSchema),
    defaultValues: defaultValues || {
      phone: "",
      employeeNumber: "",
      cardInfo: {
        avatar: "",
        name: "",
        department: "",
        position: "",
        title: "",
        company: "",
        mobile: "",
        telephone: "",
        email: "",
        address: "",
      },
    },
  })

  // 注意：头像URL变更现在直接通过FormField处理

  const handleSubmit = form.handleSubmit(async (data) => {
    await onSubmit(data)
    form.reset()
  })

  return (
    <Form {...form}>
      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="space-y-4">
          {/* 头像URL输入 */}
          <FormField
            control={form.control}
            name="cardInfo.avatar"
            render={({ field }) => (
              <FormItem>
                <AvatarUrlInput
                  existingAvatarUrl={field.value}
                  onAvatarUrlChange={(url) => field.onChange(url)}
                  onValidationChange={setIsAvatarUrlValid}
                  className="mb-6"
                />
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="grid grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="cardInfo.name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    姓名<span className="-ml-1 text-red-500">*</span>
                  </FormLabel>
                  <FormControl>
                    <Input placeholder="请输入姓名" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="cardInfo.company"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>公司</FormLabel>
                  <FormControl>
                    <Input placeholder="请输入公司名称" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="cardInfo.title"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>职位</FormLabel>
                  <FormControl>
                    <Input placeholder="请输入职位" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="cardInfo.position"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>职务</FormLabel>
                  <FormControl>
                    <Input placeholder="请输入职务" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="cardInfo.department"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>部门</FormLabel>
                  <FormControl>
                    <Input placeholder="请输入部门" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="cardInfo.mobile"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    手机号<span className="-ml-1 text-red-500">*</span>
                  </FormLabel>
                  <FormControl>
                    <Input placeholder="请输入手机号" {...field} />
                  </FormControl>
                  <FormDescription>
                    显示在名片上的手机号，如果没有设置登录手机号，此号码也将用于系统登录
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="cardInfo.telephone"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>座机</FormLabel>
                  <FormControl>
                    <Input placeholder="请输入座机号码" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="cardInfo.email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>邮箱</FormLabel>
                  <FormControl>
                    <Input placeholder="请输入邮箱" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="cardInfo.address"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>办公地址</FormLabel>
                  <FormControl>
                    <Input placeholder="请输入办公地址" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </div>

        <div className="space-y-4">
          <h3 className="font-medium text-lg">账户信息</h3>
          <div className="grid grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="phone"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>登录手机号</FormLabel>
                  <FormControl>
                    <Input placeholder="请输入登录手机号" {...field} />
                  </FormControl>
                  <FormDescription>
                    用于系统登录的手机号。如不填写，将使用名片手机号作为登录账号。
                    清空此字段将自动回落到名片手机号登录。
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="employeeNumber"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>工号</FormLabel>
                  <FormControl>
                    <Input placeholder="请输入工号" {...field} />
                  </FormControl>
                  <FormDescription>
                    用于管理端识别用户，不会传输给小程序端
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </div>

        <DialogFooter>
          <Button
            type="submit"
            disabled={isSubmitting || !isAvatarUrlValid}
            title={!isAvatarUrlValid ? "请修正头像URL错误后再提交" : ""}
          >
            {isSubmitting
              ? isEditing
                ? "更新中..."
                : "创建中..."
              : isEditing
                ? "更新用户"
                : "创建用户"}
          </Button>
        </DialogFooter>
      </form>
    </Form>
  )
}
